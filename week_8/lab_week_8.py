"""
Week 8 Lab Exercises - Data Structures and Abstract Classes

This module demonstrates:
- Tuples: Ordered, immutable collections for variable swapping
- Sets: Unordered, unique element collections for intersections
- Dictionaries: Key-value pair collections for frequency counting
- Abstract Classes: Blueprint classes with enforced interfaces

Topics Covered:
- Tuple packing/unpacking for elegant variable operations
- Set operations for efficient element comparison
- Dictionary creation and manipulation for data analysis
- Abstract base classes with @abstractmethod decorator
- Polymorphism through abstract class inheritance

Author: [<PERSON>]
Date: 2024
Version: 8.0 (Enhanced Data Structures & Abstract Classes)
"""

from abc import ABC, abstractmethod
import random
from typing import Dict, List, Set, Tuple, Any

# EXERCISE 1: TUPLES - Variable Swapping

def tuple_swap(a: Any, b: Any) -> Tuple[Any, Any]:
    """
    Task 1: Swap two variables using tuple packing/unpacking.

    This demonstrates the elegant Python way of swapping variables
    without needing a temporary variable, using tuple operations.

    Args:
        a: First variable to swap
        b: Second variable to swap

    Returns:
        Tuple[Any, Any]: Swapped values (b, a)
    """
    print(f"Before swap: a = {a}, b = {b}")
    a, b = b, a  # <PERSON>ple unpacking for swap
    print(f"After swap: a = {a}, b = {b}")
    return a, b

def demonstrate_tuple_operations() -> None:
    """Demonstrate various tuple operations and use cases."""
    print("=== Advanced Tuple Operations ===")

    # Multiple variable swap
    x, y, z = 1, 2, 3
    print(f"Before rotation: x={x}, y={y}, z={z}")
    x, y, z = z, x, y  # Rotate values
    print(f"After rotation: x={x}, y={y}, z={z}")

    # Tuple unpacking with functions
    def get_name_age():
        return "Alice", 25

    name, age = get_name_age()
    print(f"Unpacked from function: {name}, {age}")

    # Tuple as immutable data structure
    coordinates = (10.5, 20.3)
    print(f"Coordinates (immutable): {coordinates}")

# EXERCISE 2: SETS - Finding Common Elements

def common_names(set1: Set[str], set2: Set[str]) -> Set[str]:
    """
    Task 2: Find names that appear in both sets using intersection.

    Args:
        set1: First set of names
        set2: Second set of names

    Returns:
        Set[str]: Names that appear in both sets
    """
    return set1 & set2  # Set intersection operator

def demonstrate_set_operations() -> None:
    """Demonstrate comprehensive set operations."""
    print("=== Comprehensive Set Operations ===")

    # Given sets from the exercise
    set1 = {"Tom", "Jerry", "Hewey", "Dewey", "Louie"}
    set2 = {"Tom", "Garfield", "Snoopy", "Hewey", "Dewey"}

    print(f"Set 1: {set1}")
    print(f"Set 2: {set2}")

    # Various set operations
    print(f"Intersection (common): {set1 & set2}")
    print(f"Union (all unique): {set1 | set2}")
    print(f"Difference (only in set1): {set1 - set2}")
    print(f"Symmetric difference (not in both): {set1 ^ set2}")

    # Set methods
    print(f"Is Tom in set1? {('Tom' in set1)}")
    print(f"Set1 subset of union? {set1.issubset(set1 | set2)}")

# EXERCISE 3: DICTIONARIES - Histogram Function

def histogram(lst: List[Any]) -> Dict[Any, int]:
    """
    Task 3: Create a histogram dictionary from a list.

    This function counts the frequency of each element in a list
    and returns a dictionary with elements as keys and counts as values.

    Args:
        lst: List of elements to count

    Returns:
        Dict[Any, int]: Dictionary with elements as keys and counts as values

    Example:
        >>> histogram([1, 2, 3, 1, 2, 3, 4])
        {1: 2, 2: 2, 3: 2, 4: 1}
    """
    result = {}
    for item in lst:
        result[item] = result.get(item, 0) + 1
    return result

def test_histogram_function() -> None:
    """Test the histogram function with various data types."""
    print("=== Histogram Function Testing ===")

    # Test with provided example
    test_list = [1, 2, 3, 1, 2, 3, 4]
    result = histogram(test_list)
    expected = {1: 2, 2: 2, 3: 2, 4: 1}

    print(f"Input: {test_list}")
    print(f"Result: {result}")
    print(f"Expected: {expected}")

    # Assertion test
    assert result == expected, f"Test failed: {result} != {expected}"
    print("✓ Basic histogram test passed!")

    # Test with strings
    words = ["apple", "banana", "apple", "cherry", "banana", "apple"]
    word_hist = histogram(words)
    print(f"Word histogram: {word_hist}")

    # Test with mixed types
    mixed = [1, "hello", 1, "world", "hello", 2.5, 2.5]
    mixed_hist = histogram(mixed)
    print(f"Mixed type histogram: {mixed_hist}")

# EXERCISE 4: ABSTRACT CLASSES - Dice Implementation

class Dice(ABC):
    """
    Abstract base class for dice objects.

    This class demonstrates abstract classes by defining a common interface
    for all dice types while enforcing implementation of specific methods.

    Attributes:
        face (int): Current face value of the dice
    """

    def __init__(self) -> None:
        """Initialize dice with no face value."""
        self.face: int = None

    @abstractmethod
    def roll(self) -> int:
        """
        Abstract method to roll the dice.

        Must be implemented by all subclasses to return a random face value.

        Returns:
            int: The face value after rolling
        """
        pass

    def get_face(self) -> int:
        """Get the current face value."""
        return self.face

    def __str__(self) -> str:
        """String representation of the dice."""
        return f"{self.__class__.__name__}(face={self.face})"

class SixSidedDice(Dice):
    """
    Six-sided dice implementation.

    This class demonstrates concrete implementation of an abstract class,
    providing specific behavior for a standard six-sided dice.
    """

    def roll(self) -> int:
        """
        Roll the six-sided dice.

        Returns:
            int: Random number between 1 and 6 (inclusive)
        """
        self.face = random.randint(1, 6)
        return self.face

class TenSidedDice(Dice):
    """
    Ten-sided dice implementation.

    This class demonstrates polymorphism - same interface as SixSidedDice
    but different implementation for a ten-sided dice.
    """

    def roll(self) -> int:
        """
        Roll the ten-sided dice.

        Returns:
            int: Random number between 1 and 10 (inclusive)
        """
        self.face = random.randint(1, 10)
        return self.face

def roll_dice_and_histogram(dice: Dice, rolls: int = 1000) -> Dict[int, int]:
    """
    Roll dice multiple times and create a histogram of results.

    Args:
        dice: Dice object to roll
        rolls: Number of times to roll (default: 1000)

    Returns:
        Dict[int, int]: Histogram of roll results
    """
    results = [dice.roll() for _ in range(rolls)]
    return histogram(results)

def test_dice_implementations() -> None:
    """Test dice classes and demonstrate polymorphism."""
    print("=== Abstract Dice Class Testing ===")

    # Test SixSidedDice
    print("Testing SixSidedDice:")
    six_dice = SixSidedDice()
    six_histogram = roll_dice_and_histogram(six_dice, 1000)
    print(f"Six-sided dice histogram (1000 rolls): {six_histogram}")

    # Verify all faces 1-6 are present
    expected_faces = set(range(1, 7))
    actual_faces = set(six_histogram.keys())
    assert actual_faces == expected_faces, f"Missing faces: {expected_faces - actual_faces}"
    print("✓ Six-sided dice test passed!")

    # Test TenSidedDice
    print("\nTesting TenSidedDice:")
    ten_dice = TenSidedDice()
    ten_histogram = roll_dice_and_histogram(ten_dice, 1000)
    print(f"Ten-sided dice histogram (1000 rolls): {ten_histogram}")

    # Verify all faces 1-10 are present
    expected_faces = set(range(1, 11))
    actual_faces = set(ten_histogram.keys())
    assert actual_faces == expected_faces, f"Missing faces: {expected_faces - actual_faces}"
    print("✓ Ten-sided dice test passed!")

    # Demonstrate polymorphism
    print("\nDemonstrating Polymorphism:")
    dice_collection = [SixSidedDice(), TenSidedDice()]

    for dice_obj in dice_collection:
        result = dice_obj.roll()
        print(f"{dice_obj.__class__.__name__} rolled: {result}")
        print(f"Current state: {dice_obj}")

def demonstrate_abstract_class_enforcement() -> None:
    """Demonstrate that abstract classes cannot be instantiated."""
    print("=== Abstract Class Enforcement ===")

    try:
        # This should raise TypeError
        abstract_dice = Dice()
        print("ERROR: Abstract class was instantiated!")
    except TypeError as e:
        print(f"✓ Abstract class correctly prevented instantiation: {e}")

    # Show that concrete classes work fine
    concrete_dice = SixSidedDice()
    print(f"✓ Concrete class instantiated successfully: {concrete_dice}")

# MAIN EXECUTION FUNCTION

def main() -> None:
    """
    Main function to run all Week 8 exercises.

    This function demonstrates all the concepts covered:
    - Tuples for variable swapping and immutable data
    - Sets for finding intersections and unique operations
    - Dictionaries for frequency counting and key-value storage
    - Abstract classes for enforced interfaces and polymorphism
    """
    print("Week 8 Lab Exercises - Data Structures and Abstract Classes")
    print("=" * 65)

    # Exercise 1: Tuples
    print("\n1. TUPLE OPERATIONS")
    print("-" * 30)
    tuple_swap(5, 10)
    demonstrate_tuple_operations()

    # Exercise 2: Sets
    print("\n2. SET OPERATIONS")
    print("-" * 30)
    # Test with exercise data
    set1 = {"Tom", "Jerry", "Hewey", "Dewey", "Louie"}
    set2 = {"Tom", "Garfield", "Snoopy", "Hewey", "Dewey"}
    common = common_names(set1, set2)
    print(f"Common names in both sets: {common}")
    demonstrate_set_operations()

    # Exercise 3: Dictionaries
    print("\n3. DICTIONARY OPERATIONS")
    print("-" * 30)
    test_histogram_function()

    # Exercise 4: Abstract Classes
    print("\n4. ABSTRACT CLASSES")
    print("-" * 30)
    demonstrate_abstract_class_enforcement()
    test_dice_implementations()

if __name__ == "__main__":
    main()
