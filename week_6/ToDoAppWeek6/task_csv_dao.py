"""
Task CSV DAO Module - Week 6

This module implements a CSV-based Data Access Object (DAO) for the Task system.
It demonstrates persistent data storage using CSV (Comma Separated Values) format.
This provides a simple, human-readable way to store task data.

The CSV DAO provides:
- Persistent storage of task data
- Human-readable file format
- Easy data import/export capabilities
- Cross-platform compatibility

Classes:
- TaskCsvDAO: CSV file implementation for task persistence

Author: [<PERSON>]
"""


# IMPORTS


import csv  # For CSV file operations
import datetime  # For date parsing and formatting
from task import Task, RecurringTask  # Import Task classes


# TASK CSV DAO CLASS DEFINITION


class TaskCsvDAO:
    """
    CSV Data Access Object for Task persistence.
    
    This class provides CSV file-based implementation of the DAO pattern
    for storing and retrieving tasks. It handles the conversion between
    Task objects and CSV file format.
    
    Attributes:
        storage_path (str): Path to the CSV file for data storage
    """
    
    def __init__(self, storage_path: str) -> None:
        """
        Initialize the CSV DAO with a file path.
        
        Args:
            storage_path (str): Path to the CSV file for task storage
            
        Returns:
            None: Constructors don't return values
            
        Example:
            >>> dao = TaskCsvDAO("tasks.csv")
        """
        self.storage_path = storage_path
    
    def save_all_tasks(self, tasks: list[Task]) -> None:
        """
        Save all tasks to CSV file.
        
        This method writes all tasks to a CSV file with the following format:
        title,completed,date_created,date_due,task_type,interval_days
        
        Args:
            tasks (list[Task]): List of tasks to save to CSV
            
        Returns:
            None: Method writes to file but doesn't return a value
            
        Example:
            >>> dao = TaskCsvDAO("tasks.csv")
            >>> dao.save_all_tasks(task_list.tasks)
            Saved 3 tasks to tasks.csv
        """
        try:
            with open(self.storage_path, 'w', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                
                # Write header row
                writer.writerow([
                    'title', 'completed', 'date_created', 'date_due', 
                    'task_type', 'interval_days', 'completed_dates'
                ])
                
                # Write each task as a row
                for task in tasks:
                    if isinstance(task, RecurringTask):
                        # Handle recurring tasks with additional data
                        completed_dates_str = ';'.join([
                            date.isoformat() for date in task.completed_dates
                        ])
                        writer.writerow([
                            task.title,
                            task.completed,
                            task.date_created.isoformat(),
                            task.date_due.isoformat(),
                            'RecurringTask',
                            task.interval.days,
                            completed_dates_str
                        ])
                    else:
                        # Handle regular tasks
                        writer.writerow([
                            task.title,
                            task.completed,
                            task.date_created.isoformat(),
                            task.date_due.isoformat(),
                            'Task',
                            '',  # No interval for regular tasks
                            ''   # No completed dates for regular tasks
                        ])
            
            print(f"Saved {len(tasks)} tasks to {self.storage_path}")
            
        except Exception as e:
            print(f"Error saving tasks to {self.storage_path}: {e}")
    
    def get_all_tasks(self) -> list[Task]:
        """
        Load all tasks from CSV file.
        
        This method reads tasks from a CSV file and converts them back
        to Task or RecurringTask objects based on the stored data.
        
        Returns:
            list[Task]: List of tasks loaded from CSV file
            
        Example:
            >>> dao = TaskCsvDAO("tasks.csv")
            >>> tasks = dao.get_all_tasks()
            >>> print(f"Loaded {len(tasks)} tasks")
        """
        tasks = []
        
        try:
            with open(self.storage_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    try:
                        # Parse common task data
                        title = row['title']
                        completed = row['completed'].lower() == 'true'
                        date_created = datetime.datetime.fromisoformat(row['date_created'])
                        date_due = datetime.datetime.fromisoformat(row['date_due'])
                        task_type = row['task_type']
                        
                        # Create appropriate task type
                        if task_type == 'RecurringTask':
                            # Create recurring task
                            interval_days = int(row['interval_days'])
                            interval = datetime.timedelta(days=interval_days)
                            
                            task = RecurringTask(title, date_due, interval)
                            
                            # Restore completed dates
                            completed_dates_str = row['completed_dates']
                            if completed_dates_str:
                                task.completed_dates = [
                                    datetime.datetime.fromisoformat(date_str)
                                    for date_str in completed_dates_str.split(';')
                                    if date_str
                                ]
                        else:
                            # Create regular task
                            task = Task(title, date_due)
                        
                        # Set common properties
                        task.date_created = date_created
                        task.completed = completed
                        
                        tasks.append(task)
                        
                    except (ValueError, KeyError) as e:
                        print(f"Error parsing task row: {e}")
                        continue
            
            print(f"Loaded {len(tasks)} tasks from {self.storage_path}")
            
        except FileNotFoundError:
            print(f"No existing task file found at {self.storage_path}. Starting with empty task list.")
        except Exception as e:
            print(f"Error loading tasks from {self.storage_path}: {e}")
        
        return tasks
