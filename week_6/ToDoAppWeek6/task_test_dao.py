"""
Task Test DAO Module - Week 6

This module implements a test Data Access Object (DAO) for the Task system.
It demonstrates the DAO pattern for data persistence without actual file I/O.
This is useful for testing and development purposes.

The DAO pattern provides:
- Abstraction of data storage implementation
- Consistent interface for data operations
- Easy switching between different storage methods
- Separation of concerns between business logic and data access

Classes:
- TaskTestDAO: Test implementation that returns hardcoded data

Author: [<PERSON>]
"""


# IMPORTS


import datetime  # For creating test dates
from task import Task, RecurringTask  # Import Task classes


# TASK TEST DAO CLASS DEFINITION


class TaskTestDAO:
    """
    Test Data Access Object for Task persistence.
    
    This class provides a test implementation of the DAO pattern
    that returns hardcoded test data instead of reading from files.
    It's useful for testing and development without file dependencies.
    
    Attributes:
        storage_path (str): Path where data would be stored (for interface consistency)
    """
    
    def __init__(self, storage_path: str = "test_data") -> None:
        """
        Initialize the test DAO with a storage path.
        
        Args:
            storage_path (str): Path identifier (not used in test implementation)
            
        Returns:
            None: Constructors don't return values
        """
        self.storage_path = storage_path
    
    def get_all_tasks(self) -> list[Task]:
        """
        Retrieve all tasks from test data.
        
        This method returns a predefined set of test tasks for development
        and testing purposes. It simulates loading tasks from persistent storage.
        
        Returns:
            list[Task]: A list of test Task objects
            
        Example:
            >>> dao = TaskTestDAO()
            >>> tasks = dao.get_all_tasks()
            >>> print(len(tasks))  # Output: 4
        """
        # Create test tasks with various states and dates
        test_tasks = [
            Task(
                "Buy groceries", 
                datetime.datetime.now() + datetime.timedelta(days=1)
            ),
            Task(
                "Complete assignment", 
                datetime.datetime.now() + datetime.timedelta(days=3)
            ),
            Task(
                "Book doctor's appointment", 
                datetime.datetime.now() + datetime.timedelta(days=7)
            ),
            RecurringTask(
                "Weekly team meeting",
                datetime.datetime.now() + datetime.timedelta(days=2),
                datetime.timedelta(days=7)
            )
        ]
        
        # Mark some tasks as completed for testing
        test_tasks[1].mark_as_completed()  # Complete assignment is done
        
        print(f"Loaded {len(test_tasks)} test tasks from {self.storage_path}")
        return test_tasks
    
    def save_all_tasks(self, tasks: list[Task]) -> None:
        """
        Save all tasks (test implementation - no actual saving).
        
        This method simulates saving tasks to persistent storage.
        In the test implementation, it just prints confirmation.
        
        Args:
            tasks (list[Task]): List of tasks to save
            
        Returns:
            None: Method simulates saving but doesn't return a value
            
        Example:
            >>> dao = TaskTestDAO()
            >>> dao.save_all_tasks(task_list)
            Simulated saving 3 tasks to test_data
        """
        print(f"Simulated saving {len(tasks)} tasks to {self.storage_path}")
        
        # In a real implementation, this would write to a file or database
        # For testing, we just confirm the operation
        for i, task in enumerate(tasks, 1):
            print(f"  {i}. {task.title} - {'Completed' if task.completed else 'Not Completed'}")
