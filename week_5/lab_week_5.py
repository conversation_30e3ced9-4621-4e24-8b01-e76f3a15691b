# Simple Inheritance

class Vehicle:

    def __init__(self, colour, weight, max_speed, max_range=None, seats=None):
        self.colour = colour
        self.weight = weight
        self.max_speed = max_speed
        self.max_range = max_range
        self.seats = seats

    def move(self, speed):
        print(f"The vehicle is moving at {speed} km/h")

# class Car(Vehicle):

#     def move(self, speed):
#         print(f"The car is driving at {speed} km/h")

# generic_vehicle = Vehicle("red", 1000, 200)
# generic_vehicle.move(100)
# car = Car("blue", 1500, 250)
# car.move(150)

class Car(Vehicle):

    def __init__(self, colour, weight, max_speed, form_factor,  max_range=None, seats=None):
        self.colour = colour
        self.weight = weight
        self.max_speed = max_speed
        self.form_factor = form_factor
        self.max_range = max_range
        self.seats = seats
    def move(self, speed):
        print(f"The car is driving at {speed} km/h")

car = Car("blue", 1500, 250, "SUV")
car.move(150)

# Super() function


class Electric(Car):
    def __init__(self, colour, weight, max_speed, form_factor, battery_capacity):
        super().__init__(colour, weight, max_speed, form_factor)
        self.battery_capacity = battery_capacity
    def move(self, speed):
        print(f"The electric car is driving at {speed} km/h")

class Petrol(Car):
    def __init__(self, colour, weight, max_speed, form_factor, fuel_capacity):
        super().__init__(colour, weight, max_speed, form_factor)
        self.fuel_capacity = fuel_capacity
    def move(self, speed):
        print(f"The petrol car is driving at {speed} km/h")

electric_car = Electric("green", 1200, 200, "Hatchback", 100)
electric_car.move(100)
petrol_car = Petrol("red", 1500, 250, "SUV", 50)
petrol_car.move(150)
generic_vehicle = Vehicle("red", 1000, 200)
generic_vehicle.move(100)

# Task 1

class Electric(Car):
    def __init__(self, colour, weight, max_speed, form_factor, battery_capacity):
        super().__init__(colour, weight, max_speed, form_factor)
        self.battery_capacity = battery_capacity
    def move(self, speed, maximum_range):
        print(f"The electric car is driving at {speed} km/h and has a maximum range of {maximum_range} km")

class Petrol(Car):
    def __init__(self, colour, weight, max_speed, form_factor, fuel_capacity):
        super().__init__(colour, weight, max_speed, form_factor)
        self.fuel_capacity = fuel_capacity
    def move(self, speed, maximum_range):
        print(f"The petrol car is driving at {speed} km/h and has a maximum range of {maximum_range} km")

electric_car = Electric("green", 1200, 200, "Hatchback", 100)
electric_car.move(100, 100)
petrol_car = Petrol("red", 1500, 250, "SUV", 50)
petrol_car.move(150, 170)
generic_vehicle = Vehicle("red", 1000, 200)
generic_vehicle.move(100)

# Kwargs
class Car(Vehicle):
    def __init__(self, colour, weight, max_speed, form_factor, **kwargs):
        super().__init__(colour, weight, max_speed, **kwargs)
        self.form_factor = form_factor

# Task 2
class Electric(Car):
    def __init__(self, colour, weight, max_speed, form_factor, battery_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.battery_capacity = battery_capacity
    def move(self, speed):
        print(f"The electric car is driving at {speed} km/h and has a maximum range of {self.max_range} km")

class Petrol(Car):
    def __init__(self, colour, weight, max_speed, form_factor, fuel_capacity, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor, **kwargs)
        self.fuel_capacity = fuel_capacity
    def move(self, speed, maximum_range):
        print(f"The petrol car is driving at {speed} km/h and has a maximum range of {self.max_range} km")

petrol_car = Petrol("red", 1500, 250, "SUV", 50)
petrol_car.move(150, 170)
generic_vehicle = Vehicle("red", 1000, 200)
generic_vehicle.move(100)
generic_electric_car = Electric("red", 1000, 200, "SUV", 100, max_range=500, seats=5)
generic_electric_car.move(100)
print(generic_electric_car.seats)

# Task 3
class Plane(Vehicle):
    def __init__(self, colour, weight, max_speed, num_engines, wingspan, **kwargs):
        super().__init__(colour, weight, max_speed, **kwargs)
        self.num_engines = num_engines
        self.wingspan = wingspan
    def move(self, speed):
        print(f"The plane is flying at {speed} km/h")

class Propeller(Plane):
    def __init__(self, colour, weight, max_speed, num_engines, num_propellers, propeller_diameter, **kwargs):
        super().__init__(colour, weight, max_speed, num_engines, **kwargs)
        self.num_propellers = num_propellers
        self.propeller_diameter = propeller_diameter
    def move(self, speed):
        print(f"The propeller plane is flying at {speed} km/h")

class Jet(Plane):
    def __init__(self, colour, weight, max_speed, num_engines, num_wings, engine_thrust, **kwargs):
        super().__init__(colour, weight, max_speed, num_engines, **kwargs)
        self.num_wings = num_wings
        self.engine_thrust = engine_thrust
    def move(self, speed):
        print(f"The jet is flying at {speed} km/h")

propeller_plane = Propeller("red", 1000, 200, 2, 4, 100)
propeller_plane.move(100)
jet_plane = Jet("blue", 1000, 200, 2, 2, 1000)
jet_plane.move(200)
generic_plane = Plane("green", 1000, 200, 2, 10)
generic_plane.move(100)


# Multiple Inheritance

class FlyingCar(Car, Plane):
    def __init__(self, colour, weight, max_speed, form_factor, wingspan, **kwargs):
        super().__init__(colour, weight, max_speed, form_factor=form_factor, wingspan=wingspan, **kwargs)
    def move(self, speed):
        print(f"The flying car is driving or flying at {speed} km/h")




